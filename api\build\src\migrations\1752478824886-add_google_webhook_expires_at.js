"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddGoogleWebhookExpiresAt1752478824886 = void 0;
class AddGoogleWebhookExpiresAt1752478824886 {
    constructor() {
        this.name = 'AddGoogleWebhookExpiresAt1752478824886';
    }
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "users" ADD "google_webhook_expires_at" TIMESTAMP WITH TIME ZONE`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "users" DROP COLUMN "google_webhook_expires_at"`);
    }
}
exports.AddGoogleWebhookExpiresAt1752478824886 = AddGoogleWebhookExpiresAt1752478824886;
//# sourceMappingURL=1752478824886-add_google_webhook_expires_at.js.map