{"version": 3, "file": "mock-google-calendar.service.js", "sourceRoot": "", "sources": ["../../../../src/utils/google-calendar/mock-google-calendar.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA,2CAAoD;AAwC7C,IAAM,yBAAyB,iCAA/B,MAAM,yBAAyB;IAMrC;QALiB,WAAM,GAAG,IAAI,eAAM,CAAC,2BAAyB,CAAC,IAAI,CAAC,CAAC;QACpD,eAAU,GAAG,IAAI,GAAG,EAA2B,CAAC;QAChD,kBAAa,GAAG,IAAI,GAAG,EAAwB,CAAC;QACzD,mBAAc,GAAG,CAAC,CAAC;QAG1B,IAAI,CAAC,kBAAkB,EAAE,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,aAAa,CAAC,WAAmB;QACtC,IAAI,CAAC,mBAAmB,CAAC,WAAW,CAAC,CAAC;QACtC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,yBAAyB,CAAC,CAAC;QAE7C,OAAO;YACN,KAAK,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC;SAC9C,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,WAAW,CAChB,WAAmB,EACnB,UAAkB,EAClB,SAAmC;QAEnC,IAAI,CAAC,mBAAmB,CAAC,WAAW,CAAC,CAAC;QACtC,IAAI,CAAC,sBAAsB,CAAC,UAAU,CAAC,CAAC;QAExC,MAAM,OAAO,GAAG,cAAc,IAAI,CAAC,cAAc,EAAE,EAAE,CAAC;QACtD,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;QAErC,MAAM,KAAK,GAAoB;YAC9B,EAAE,EAAE,OAAO;YACX,OAAO,EAAE,SAAS,CAAC,OAAO,IAAI,gBAAgB;YAC9C,WAAW,EAAE,SAAS,CAAC,WAAW;YAClC,KAAK,EAAE,SAAS,CAAC,KAAK,IAAI;gBACzB,QAAQ,EAAE,GAAG;gBACb,QAAQ,EAAE,KAAK;aACf;YACD,GAAG,EAAE,SAAS,CAAC,GAAG,IAAI;gBACrB,QAAQ,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,OAAO,CAAC,CAAC,WAAW,EAAE,EAAE,UAAU;gBAClE,QAAQ,EAAE,KAAK;aACf;YACD,SAAS,EAAE,SAAS,CAAC,SAAS,IAAI,EAAE;YACpC,MAAM,EAAE,WAAW;YACnB,OAAO,EAAE,GAAG;YACZ,OAAO,EAAE;gBACR,KAAK,EAAE,iBAAiB;gBACxB,WAAW,EAAE,WAAW;aACxB;YACD,SAAS,EAAE;gBACV,KAAK,EAAE,iBAAiB;gBACxB,WAAW,EAAE,WAAW;aACxB;SACD,CAAC;QAEF,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QACpC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,uBAAuB,OAAO,EAAE,EAAE,EAAE,OAAO,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QAEhF,OAAO,KAAK,CAAC;IACd,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,WAAW,CAChB,WAAmB,EACnB,UAAkB,EAClB,OAAe,EACf,SAAmC;QAEnC,IAAI,CAAC,mBAAmB,CAAC,WAAW,CAAC,CAAC;QACtC,IAAI,CAAC,sBAAsB,CAAC,UAAU,CAAC,CAAC;QAExC,MAAM,aAAa,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QACnD,IAAI,CAAC,aAAa,EAAE,CAAC;YACpB,MAAM,IAAI,KAAK,CAAC,SAAS,OAAO,YAAY,CAAC,CAAC;QAC/C,CAAC;QAED,MAAM,YAAY,GAAoB;YACrC,GAAG,aAAa;YAChB,GAAG,SAAS;YACZ,EAAE,EAAE,OAAO,EAAE,uBAAuB;YACpC,OAAO,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACjC,CAAC;QAEF,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;QAC3C,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,uBAAuB,OAAO,EAAE,EAAE,EAAE,OAAO,EAAE,YAAY,CAAC,OAAO,EAAE,CAAC,CAAC;QAEvF,OAAO,YAAY,CAAC;IACrB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,WAAW,CAChB,WAAmB,EACnB,UAAkB,EAClB,OAAe;QAEf,IAAI,CAAC,mBAAmB,CAAC,WAAW,CAAC,CAAC;QACtC,IAAI,CAAC,sBAAsB,CAAC,UAAU,CAAC,CAAC;QAExC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC;YACnC,MAAM,IAAI,KAAK,CAAC,SAAS,OAAO,YAAY,CAAC,CAAC;QAC/C,CAAC;QAED,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QAChC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,uBAAuB,OAAO,EAAE,CAAC,CAAC;IACrD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,QAAQ,CACb,WAAmB,EACnB,UAAkB,EAClB,OAAe;QAEf,IAAI,CAAC,mBAAmB,CAAC,WAAW,CAAC,CAAC;QACtC,IAAI,CAAC,sBAAsB,CAAC,UAAU,CAAC,CAAC;QAExC,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAC3C,IAAI,CAAC,KAAK,EAAE,CAAC;YACZ,MAAM,IAAI,KAAK,CAAC,SAAS,OAAO,YAAY,CAAC,CAAC;QAC/C,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,yBAAyB,OAAO,EAAE,CAAC,CAAC;QACtD,OAAO,KAAK,CAAC;IACd,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,eAAe,CACpB,WAAmB,EACnB,UAAkB,EAClB,UAAkB;QAElB,IAAI,CAAC,mBAAmB,CAAC,WAAW,CAAC,CAAC;QACtC,IAAI,CAAC,sBAAsB,CAAC,UAAU,CAAC,CAAC;QAExC,MAAM,SAAS,GAAG,gBAAgB,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC;QAC/C,MAAM,UAAU,GAAG,iBAAiB,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC;QAEjD,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B,SAAS,iBAAiB,UAAU,EAAE,CAAC,CAAC;QAEtF,OAAO,EAAE,EAAE,EAAE,SAAS,EAAE,UAAU,EAAE,CAAC;IACtC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,WAAW,CAChB,WAAmB,EACnB,SAAiB,EACjB,UAAkB;QAElB,IAAI,CAAC,mBAAmB,CAAC,WAAW,CAAC,CAAC;QACtC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,yBAAyB,SAAS,EAAE,CAAC,CAAC;IACzD,CAAC;IAED;;OAEG;IACH,iBAAiB;QAChB,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC;QAC9C,KAAa,CAAC,QAAQ,GAAG;YACzB,MAAM,EAAE,GAAG;YACX,IAAI,EAAE;gBACL,KAAK,EAAE;oBACN,IAAI,EAAE,GAAG;oBACT,OAAO,EAAE,qBAAqB;iBAC9B;aACD;SACD,CAAC;QACF,MAAM,KAAK,CAAC;IACb,CAAC;IAED;;OAEG;IACH,mBAAmB;QAClB,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,cAAc,CAAC,CAAC;QACvC,KAAa,CAAC,QAAQ,GAAG;YACzB,MAAM,EAAE,GAAG;YACX,IAAI,EAAE;gBACL,KAAK,EAAE;oBACN,IAAI,EAAE,GAAG;oBACT,OAAO,EAAE,qBAAqB;iBAC9B;aACD;SACD,CAAC;QACF,MAAM,KAAK,CAAC;IACb,CAAC;IAED;;OAEG;IACH,gBAAgB;QACf,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,CAAC;IAC7C,CAAC;IAED;;OAEG;IACH,aAAa;QACZ,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;QACxB,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;QAC3B,IAAI,CAAC,kBAAkB,EAAE,CAAC;IAC3B,CAAC;IAEO,kBAAkB;QACzB,6CAA6C;QAC7C,MAAM,eAAe,GAAiB;YACrC,EAAE,EAAE,SAAS;YACb,OAAO,EAAE,eAAe;YACxB,WAAW,EAAE,uBAAuB;YACpC,OAAO,EAAE,IAAI;YACb,UAAU,EAAE,OAAO;SACnB,CAAC;QAEF,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,SAAS,EAAE,eAAe,CAAC,CAAC;QAEnD,2BAA2B;QAC3B,MAAM,iBAAiB,GAAiB;YACvC,EAAE,EAAE,WAAW;YACf,OAAO,EAAE,eAAe;YACxB,WAAW,EAAE,qBAAqB;YAClC,OAAO,EAAE,KAAK;YACd,UAAU,EAAE,QAAQ;SACpB,CAAC;QAEF,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,WAAW,EAAE,iBAAiB,CAAC,CAAC;IACxD,CAAC;IAEO,mBAAmB,CAAC,WAAmB;QAC9C,IAAI,CAAC,WAAW,IAAI,WAAW,KAAK,eAAe,EAAE,CAAC;YACrD,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAC5B,CAAC;QAED,IAAI,WAAW,KAAK,oBAAoB,EAAE,CAAC;YAC1C,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAC1B,CAAC;IACF,CAAC;IAEO,sBAAsB,CAAC,UAAkB;QAChD,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE,CAAC;YACzC,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,YAAY,UAAU,YAAY,CAAC,CAAC;YAC3D,KAAa,CAAC,QAAQ,GAAG;gBACzB,MAAM,EAAE,GAAG;gBACX,IAAI,EAAE;oBACL,KAAK,EAAE;wBACN,IAAI,EAAE,GAAG;wBACT,OAAO,EAAE,oBAAoB;qBAC7B;iBACD;aACD,CAAC;YACF,MAAM,KAAK,CAAC;QACb,CAAC;IACF,CAAC;CACD,CAAA;AA1QY,8DAAyB;oCAAzB,yBAAyB;IADrC,IAAA,mBAAU,GAAE;;GACA,yBAAyB,CA0QrC"}