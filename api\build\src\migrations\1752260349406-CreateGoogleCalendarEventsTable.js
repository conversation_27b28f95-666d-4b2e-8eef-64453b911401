"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateGoogleCalendarEventsTable1752260349406 = void 0;
class CreateGoogleCalendarEventsTable1752260349406 {
    constructor() {
        this.name = 'CreateGoogleCalendarEventsTable1752260349406';
    }
    async up(queryRunner) {
        await queryRunner.query(`
      CREATE TABLE "google_calendar_events" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "user_id" uuid NOT NULL,
        "event_id" character varying NOT NULL,
        "calendar_id" character varying,
        "summary" character varying(1024),
        "description" text,
        "start_time" TIMESTAMPTZ NOT NULL,
        "end_time" TIMESTAMPTZ NOT NULL,
        "status" character varying(50),
        "raw" jsonb,
        "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
        CONSTRAINT "pk_google_calendar_events" PRIMARY KEY ("id")
      );
    `);
        await queryRunner.query(`
      CREATE UNIQUE INDEX "idx_gc_events_user_event" ON "google_calendar_events" ("user_id", "event_id");
    `);
        await queryRunner.query(`
      CREATE INDEX "idx_gc_events_user_start" ON "google_calendar_events" ("user_id", "start_time");
    `);
        await queryRunner.query(`
      ALTER TABLE "google_calendar_events"
      ADD CONSTRAINT "fk_gc_events_user" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE;
    `);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "google_calendar_events" DROP CONSTRAINT "fk_gc_events_user"`);
        await queryRunner.query(`DROP INDEX "idx_gc_events_user_start"`);
        await queryRunner.query(`DROP INDEX "idx_gc_events_user_event"`);
        await queryRunner.query(`DROP TABLE "google_calendar_events"`);
    }
}
exports.CreateGoogleCalendarEventsTable1752260349406 = CreateGoogleCalendarEventsTable1752260349406;
//# sourceMappingURL=1752260349406-CreateGoogleCalendarEventsTable.js.map