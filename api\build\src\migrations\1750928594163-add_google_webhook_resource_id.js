"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddGoogleWebhookResourceId1750928594163 = void 0;
class AddGoogleWebhookResourceId1750928594163 {
    constructor() {
        this.name = 'AddGoogleWebhookResourceId1750928594163';
    }
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "users" ADD "google_webhook_resource_id" character varying`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "users" DROP COLUMN "google_webhook_resource_id"`);
    }
}
exports.AddGoogleWebhookResourceId1750928594163 = AddGoogleWebhookResourceId1750928594163;
//# sourceMappingURL=1750928594163-add_google_webhook_resource_id.js.map