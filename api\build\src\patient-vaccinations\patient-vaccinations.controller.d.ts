import { <PERSON><PERSON>og<PERSON> } from '../utils/logger/winston-logger.service';
import { PatientVaccinationsService } from './patient-vaccinations.service';
import { ApiDocumentationBase } from '../base/api-documentation-base';
import { CreatePatientVaccinationDto } from './dto/create-patient-vaccination.dto';
import { UpdatePatientVaccinationDto } from './dto/update-patient-vaccination.dto';
export declare class PatientVaccinationsController extends ApiDocumentationBase {
    private readonly logger;
    private readonly patientVaccinationsService;
    constructor(logger: WinstonLogger, patientVaccinationsService: PatientVaccinationsService);
    create(createPaitentVaccinationDto: CreatePatientVaccinationDto): Promise<CreatePatientVaccinationDto>;
    get(patientId: string): Promise<import("./entities/patient-vaccinations.entity").PatientVaccination[]>;
    update(id: string, updatePatientVaccinationDto: UpdatePatientVaccinationDto): Promise<{
        patientId: string;
        appointmentId?: string;
        systemGenerated: boolean;
        doctorName?: string;
        vaccineName: string;
        vaccinationDate: Date;
        reportUrl?: string;
        urlMeta?: object;
        id: string;
        vaccinationId: string;
        vaccineId: string;
        createdAt: Date;
        updatedAt: Date;
        patient: import("../patients/entities/patient.entity").Patient;
        appointment?: import("../appointments/entities/appointment.entity").AppointmentEntity;
        vaccination: import("../clinic-vaccinations/entities/clinic-vaccination.entity").ClinicVaccinationEntity;
        deletedAt?: Date;
        removedFromInvoice: boolean;
    } & import("./entities/patient-vaccinations.entity").PatientVaccination>;
}
