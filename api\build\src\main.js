"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.bootstrap = bootstrap;
require("newrelic");
const config_1 = require("@nestjs/config");
const common_1 = require("@nestjs/common");
const core_1 = require("@nestjs/core");
const cookieParser = require("cookie-parser");
const helmet_1 = require("helmet");
const app_module_1 = require("./app.module");
const api_documentation_base_1 = require("./base/api-documentation-base");
const http_exception_filter_1 = require("./utils/http-exception.filter");
const winston_logger_service_1 = require("./utils/logger/winston-logger.service");
async function bootstrap() {
    var _a;
    try {
        // Set the SERVICE_TYPE environment variable to identify this as an API service
        process.env.SERVICE_TYPE = 'api';
        const app = await core_1.NestFactory.create(app_module_1.AppModule.register({ isSqsEnabled: false }));
        // const app = await NestFactory.create(AppModule);
        app.enableCors();
        app.setGlobalPrefix('api');
        app.useLogger(app.get(winston_logger_service_1.WinstonLogger));
        app.use(cookieParser());
        app.use((0, helmet_1.default)());
        app.useGlobalFilters(new http_exception_filter_1.HttpExceptionFilter(app.get(winston_logger_service_1.WinstonLogger)));
        app.useGlobalPipes(new common_1.ValidationPipe());
        const configService = app.get(config_1.ConfigService);
        // Initialising Swagger
        const environment = configService.get('app.nodeEnv') || 'development';
        if (!['prod', 'uat'].includes(environment)) {
            api_documentation_base_1.ApiDocumentationBase.initApiDocumentation(app);
        }
        // ApiDocumentationBase.initApiDocumentation(app);
        const port = (_a = configService === null || configService === void 0 ? void 0 : configService.get('app.apiPort')) !== null && _a !== void 0 ? _a : 8000;
        // console.log('Port is at', port);
        await app
            .listen(port)
            .then(() => console.log('Port is at', port))
            .catch(err => console.log('Error', err));
        return app;
    }
    catch (err) {
        console.log(err);
    }
    return null;
}
bootstrap();
//# sourceMappingURL=main.js.map