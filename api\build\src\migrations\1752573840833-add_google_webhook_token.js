"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddGoogleWebhookToken1752573840833 = void 0;
class AddGoogleWebhookToken1752573840833 {
    constructor() {
        this.name = 'AddGoogleWebhookToken1752573840833';
    }
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "users" ADD "google_webhook_token" varchar`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "users" DROP COLUMN "google_webhook_token"`);
    }
}
exports.AddGoogleWebhookToken1752573840833 = AddGoogleWebhookToken1752573840833;
//# sourceMappingURL=1752573840833-add_google_webhook_token.js.map