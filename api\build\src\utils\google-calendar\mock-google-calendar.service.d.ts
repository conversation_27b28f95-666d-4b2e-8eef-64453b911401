export interface MockGoogleEvent {
    id: string;
    summary: string;
    description?: string;
    start: {
        dateTime: string;
        timeZone: string;
    };
    end: {
        dateTime: string;
        timeZone: string;
    };
    attendees?: Array<{
        email: string;
        displayName?: string;
        responseStatus?: string;
    }>;
    status: 'confirmed' | 'tentative' | 'cancelled';
    updated: string;
    creator: {
        email: string;
        displayName?: string;
    };
    organizer: {
        email: string;
        displayName?: string;
    };
}
export interface MockCalendar {
    id: string;
    summary: string;
    description?: string;
    primary?: boolean;
    accessRole: string;
}
export declare class MockGoogleCalendarService {
    private readonly logger;
    private readonly mockEvents;
    private readonly mockCalendars;
    private eventIdCounter;
    constructor();
    /**
     * Mock calendar list
     */
    listCalendars(accessToken: string): Promise<{
        items: MockCalendar[];
    }>;
    /**
     * Mock event creation
     */
    createEvent(accessToken: string, calendarId: string, eventData: Partial<MockGoogleEvent>): Promise<MockGoogleEvent>;
    /**
     * Mock event update
     */
    updateEvent(accessToken: string, calendarId: string, eventId: string, eventData: Partial<MockGoogleEvent>): Promise<MockGoogleEvent>;
    /**
     * Mock event deletion
     */
    deleteEvent(accessToken: string, calendarId: string, eventId: string): Promise<void>;
    /**
     * Mock event retrieval
     */
    getEvent(accessToken: string, calendarId: string, eventId: string): Promise<MockGoogleEvent>;
    /**
     * Mock webhook registration
     */
    registerWebhook(accessToken: string, calendarId: string, webhookUrl: string): Promise<{
        id: string;
        resourceId: string;
    }>;
    /**
     * Mock webhook deregistration
     */
    stopWebhook(accessToken: string, webhookId: string, resourceId: string): Promise<void>;
    /**
     * Simulate rate limiting for testing
     */
    simulateRateLimit(): void;
    /**
     * Simulate authentication failure for testing
     */
    simulateAuthFailure(): void;
    /**
     * Get all mock events (for testing purposes)
     */
    getAllMockEvents(): MockGoogleEvent[];
    /**
     * Clear all mock data (for testing cleanup)
     */
    clearMockData(): void;
    private initializeMockData;
    private validateAccessToken;
    private validateCalendarAccess;
}
