"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var MockGoogleCalendarService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.MockGoogleCalendarService = void 0;
const common_1 = require("@nestjs/common");
let MockGoogleCalendarService = MockGoogleCalendarService_1 = class MockGoogleCalendarService {
    constructor() {
        this.logger = new common_1.Logger(MockGoogleCalendarService_1.name);
        this.mockEvents = new Map();
        this.mockCalendars = new Map();
        this.eventIdCounter = 1;
        this.initializeMockData();
    }
    /**
     * Mock calendar list
     */
    async listCalendars(accessToken) {
        this.validateAccessToken(accessToken);
        this.logger.debug('Mock: Listing calendars');
        return {
            items: Array.from(this.mockCalendars.values())
        };
    }
    /**
     * Mock event creation
     */
    async createEvent(accessToken, calendarId, eventData) {
        this.validateAccessToken(accessToken);
        this.validateCalendarAccess(calendarId);
        const eventId = `mock_event_${this.eventIdCounter++}`;
        const now = new Date().toISOString();
        const event = {
            id: eventId,
            summary: eventData.summary || 'Untitled Event',
            description: eventData.description,
            start: eventData.start || {
                dateTime: now,
                timeZone: 'UTC'
            },
            end: eventData.end || {
                dateTime: new Date(Date.now() + 3600000).toISOString(), // +1 hour
                timeZone: 'UTC'
            },
            attendees: eventData.attendees || [],
            status: 'confirmed',
            updated: now,
            creator: {
                email: '<EMAIL>',
                displayName: 'Test User'
            },
            organizer: {
                email: '<EMAIL>',
                displayName: 'Test User'
            }
        };
        this.mockEvents.set(eventId, event);
        this.logger.debug(`Mock: Created event ${eventId}`, { summary: event.summary });
        return event;
    }
    /**
     * Mock event update
     */
    async updateEvent(accessToken, calendarId, eventId, eventData) {
        this.validateAccessToken(accessToken);
        this.validateCalendarAccess(calendarId);
        const existingEvent = this.mockEvents.get(eventId);
        if (!existingEvent) {
            throw new Error(`Event ${eventId} not found`);
        }
        const updatedEvent = {
            ...existingEvent,
            ...eventData,
            id: eventId, // Preserve original ID
            updated: new Date().toISOString()
        };
        this.mockEvents.set(eventId, updatedEvent);
        this.logger.debug(`Mock: Updated event ${eventId}`, { summary: updatedEvent.summary });
        return updatedEvent;
    }
    /**
     * Mock event deletion
     */
    async deleteEvent(accessToken, calendarId, eventId) {
        this.validateAccessToken(accessToken);
        this.validateCalendarAccess(calendarId);
        if (!this.mockEvents.has(eventId)) {
            throw new Error(`Event ${eventId} not found`);
        }
        this.mockEvents.delete(eventId);
        this.logger.debug(`Mock: Deleted event ${eventId}`);
    }
    /**
     * Mock event retrieval
     */
    async getEvent(accessToken, calendarId, eventId) {
        this.validateAccessToken(accessToken);
        this.validateCalendarAccess(calendarId);
        const event = this.mockEvents.get(eventId);
        if (!event) {
            throw new Error(`Event ${eventId} not found`);
        }
        this.logger.debug(`Mock: Retrieved event ${eventId}`);
        return event;
    }
    /**
     * Mock webhook registration
     */
    async registerWebhook(accessToken, calendarId, webhookUrl) {
        this.validateAccessToken(accessToken);
        this.validateCalendarAccess(calendarId);
        const webhookId = `mock_webhook_${Date.now()}`;
        const resourceId = `mock_resource_${Date.now()}`;
        this.logger.debug(`Mock: Registered webhook ${webhookId} for calendar ${calendarId}`);
        return { id: webhookId, resourceId };
    }
    /**
     * Mock webhook deregistration
     */
    async stopWebhook(accessToken, webhookId, resourceId) {
        this.validateAccessToken(accessToken);
        this.logger.debug(`Mock: Stopped webhook ${webhookId}`);
    }
    /**
     * Simulate rate limiting for testing
     */
    simulateRateLimit() {
        const error = new Error('Rate limit exceeded');
        error.response = {
            status: 429,
            data: {
                error: {
                    code: 429,
                    message: 'Rate limit exceeded'
                }
            }
        };
        throw error;
    }
    /**
     * Simulate authentication failure for testing
     */
    simulateAuthFailure() {
        const error = new Error('Unauthorized');
        error.response = {
            status: 401,
            data: {
                error: {
                    code: 401,
                    message: 'Invalid credentials'
                }
            }
        };
        throw error;
    }
    /**
     * Get all mock events (for testing purposes)
     */
    getAllMockEvents() {
        return Array.from(this.mockEvents.values());
    }
    /**
     * Clear all mock data (for testing cleanup)
     */
    clearMockData() {
        this.mockEvents.clear();
        this.mockCalendars.clear();
        this.initializeMockData();
    }
    initializeMockData() {
        // Initialize with a default primary calendar
        const primaryCalendar = {
            id: 'primary',
            summary: 'Test Calendar',
            description: 'Primary test calendar',
            primary: true,
            accessRole: 'owner'
        };
        this.mockCalendars.set('primary', primaryCalendar);
        // Add a secondary calendar
        const secondaryCalendar = {
            id: 'secondary',
            summary: 'Work Calendar',
            description: 'Work-related events',
            primary: false,
            accessRole: 'writer'
        };
        this.mockCalendars.set('secondary', secondaryCalendar);
    }
    validateAccessToken(accessToken) {
        if (!accessToken || accessToken === 'invalid_token') {
            this.simulateAuthFailure();
        }
        if (accessToken === 'rate_limited_token') {
            this.simulateRateLimit();
        }
    }
    validateCalendarAccess(calendarId) {
        if (!this.mockCalendars.has(calendarId)) {
            const error = new Error(`Calendar ${calendarId} not found`);
            error.response = {
                status: 404,
                data: {
                    error: {
                        code: 404,
                        message: 'Calendar not found'
                    }
                }
            };
            throw error;
        }
    }
};
exports.MockGoogleCalendarService = MockGoogleCalendarService;
exports.MockGoogleCalendarService = MockGoogleCalendarService = MockGoogleCalendarService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [])
], MockGoogleCalendarService);
//# sourceMappingURL=mock-google-calendar.service.js.map