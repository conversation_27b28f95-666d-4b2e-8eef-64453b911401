"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DiagnosticTemplatesModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const diagnostic_template_entity_1 = require("./entities/diagnostic-template.entity");
const winston_logger_service_1 = require("../utils/logger/winston-logger.service");
const diagnostic_note_controlller_1 = require("./diagnostic-note.controlller");
const diagnostic_note_service_1 = require("./diagnostic-note.service");
const role_module_1 = require("../roles/role.module");
const diagnostic_note_entity_1 = require("./entities/diagnostic-note.entity");
const lab_report_entity_1 = require("../clinic-lab-report/entities/lab-report.entity");
const clinic_lab_report_entity_1 = require("../clinic-lab-report/entities/clinic-lab-report.entity");
const patients_service_1 = require("../patients/patients.service");
const s3_service_1 = require("../utils/aws/s3/s3.service");
const patient_entity_1 = require("../patients/entities/patient.entity");
const patient_owner_entity_1 = require("../patients/entities/patient-owner.entity");
const owners_service_1 = require("../owners/owners.service");
const global_reminders_service_1 = require("../patient-global-reminders/global-reminders.service");
const global_owner_entity_1 = require("../owners/entities/global-owner.entity");
const owner_brand_entity_1 = require("../owners/entities/owner-brand.entity");
const global_reminder_rule_entity_1 = require("../patient-global-reminders/entities/global-reminder-rule.entity");
const patient_reminder_entity_1 = require("../patient-reminders/entities/patient-reminder.entity");
const brand_entity_1 = require("../brands/entities/brand.entity");
const whatsapp_module_1 = require("../utils/whatsapp-integration/whatsapp.module");
const ses_module_1 = require("../utils/aws/ses/ses.module");
const pet_transfer_history_entity_1 = require("../owners/entities/pet-transfer-history.entity");
const payment_details_entity_1 = require("../payment-details/entities/payment-details.entity");
const invoice_entity_1 = require("../invoice/entities/invoice.entity");
const appointment_entity_1 = require("../appointments/entities/appointment.entity");
const appointment_details_entity_1 = require("../appointments/entities/appointment-details.entity");
const socket_module_1 = require("../socket/socket.module");
let DiagnosticTemplatesModule = class DiagnosticTemplatesModule {
};
exports.DiagnosticTemplatesModule = DiagnosticTemplatesModule;
exports.DiagnosticTemplatesModule = DiagnosticTemplatesModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([
                diagnostic_template_entity_1.DiagnosticTemplate,
                diagnostic_note_entity_1.DiagnosticNote,
                lab_report_entity_1.LabReport,
                clinic_lab_report_entity_1.ClinicLabReport,
                patient_entity_1.Patient,
                patient_owner_entity_1.PatientOwner,
                global_owner_entity_1.GlobalOwner,
                owner_brand_entity_1.OwnerBrand,
                global_reminder_rule_entity_1.GlobalReminderRule,
                patient_reminder_entity_1.PatientReminder,
                brand_entity_1.Brand,
                pet_transfer_history_entity_1.PetTransferHistory,
                payment_details_entity_1.PaymentDetailsEntity,
                invoice_entity_1.InvoiceEntity,
                appointment_entity_1.AppointmentEntity,
                appointment_details_entity_1.AppointmentDetailsEntity
            ]),
            role_module_1.RoleModule,
            whatsapp_module_1.WhatsappModule,
            ses_module_1.SESModule,
            socket_module_1.SocketModule
        ],
        providers: [
            diagnostic_note_service_1.DiagnosticTemplatesService,
            winston_logger_service_1.WinstonLogger,
            patients_service_1.PatientsService,
            s3_service_1.S3Service,
            owners_service_1.OwnersService,
            global_reminders_service_1.GlobalReminderService
        ],
        controllers: [diagnostic_note_controlller_1.DiagnosticTemplatesController],
        exports: [diagnostic_note_service_1.DiagnosticTemplatesService]
    })
], DiagnosticTemplatesModule);
//# sourceMappingURL=diagnostic-note.module.js.map