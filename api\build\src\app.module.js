"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var AppModule_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppModule = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const app_controller_1 = require("./app.controller");
const app_service_1 = require("./app.service");
const auth_module_1 = require("./auth/auth.module");
const app_config_1 = require("./config/app.config");
const aws_config_1 = require("./config/aws.config");
const config_2 = require("./config/config");
const database_config_1 = require("./config/database.config");
const google_config_1 = require("./config/google.config");
const database_module_1 = require("./database/database.module");
const health_module_1 = require("./health/health.module");
const users_module_1 = require("./users/users.module");
const aws_module_1 = require("./utils/aws/aws-module");
const logger_module_1 = require("./utils/logger/logger-module");
const logRequest_middleware_1 = require("./utils/middlewares/logRequest.middleware");
const middlewares_module_1 = require("./utils/middlewares/middlewares.module");
const rate_limiting_middleware_1 = require("./utils/middlewares/rate-limiting.middleware");
const version_check_module_1 = require("./app-versioning/version-check.module");
const patients_module_1 = require("./patients/patients.module");
const owners_module_1 = require("./owners/owners.module");
const patientAlerts_module_1 = require("./patient-alerts/patientAlerts.module");
const appointments_module_1 = require("./appointments/appointments.module");
const clinic_module_1 = require("./clinics/clinic.module");
const clinic_lab_report_module_1 = require("./clinic-lab-report/clinic-lab-report.module");
const clinic_medications_module_1 = require("./clinic-medications/clinic-medications.module");
const clinic_plans_module_1 = require("./clinic-plans/clinic-plans.module");
const appointment_assessment_module_1 = require("./appointment-assessment/appointment-assessment.module");
const tasks_module_1 = require("./tasks/tasks.module");
const cors = require("cors");
const s3_module_1 = require("./utils/aws/s3/s3.module");
const ses_module_1 = require("./utils/aws/ses/ses.module");
const chat_room_module_1 = require("./chat-room/chat-room.module");
const user_otps_module_1 = require("./user-otps/user-otps.module");
const brands_module_1 = require("./brands/brands.module");
const long_term_medications_module_1 = require("./long-term-medications/long-term-medications.module");
const role_module_1 = require("./roles/role.module");
const clinic_alerts_module_1 = require("./clinic-alerts/clinic-alerts.module");
const schedule_1 = require("@nestjs/schedule");
const cart_item_module_1 = require("./cart-items/cart-item.module");
const clinic_consumables_module_1 = require("./clinic-consumables/clinic-consumables.module");
const clinic_products_module_1 = require("./clinic-products/clinic-products.module");
const clinic_services_module_1 = require("./clinic-services/clinic-services.module");
const clinic_vaccinations_module_1 = require("./clinic-vaccinations/clinic-vaccinations.module");
const patient_vaccinations_module_1 = require("./patient-vaccinations/patient-vaccinations.module");
const carts_module_1 = require("./carts/carts.module");
const invoice_module_1 = require("./invoice/invoice.module");
const payment_details_module_1 = require("./payment-details/payment-details.module");
const data_module_1 = require("./data-migration/data.module");
const whatsapp_module_1 = require("./utils/whatsapp-integration/whatsapp.module");
const clinic_idexx_module_1 = require("./clinic_integrations/idexx/clinic-idexx.module");
const emr_module_1 = require("./emr/emr.module");
const ai_module_1 = require("./utils/ai-integration/soap-ai/ai.module");
const patient_reminder_module_1 = require("./patient-reminders/patient-reminder.module");
const document_library_module_1 = require("./document-library/document-library.module");
const patient_document_libraries_module_1 = require("./patient-document-libraries/patient-document-libraries.module");
const global_reminders_module_1 = require("./patient-global-reminders/global-reminders.module");
const sqs_module_1 = require("./utils/aws/sqs/sqs.module");
const newrelic_middleware_1 = require("./utils/middlewares/newrelic.middleware");
const diagnostic_note_module_1 = require("./diagnostic-notes-templates/diagnostic-note.module");
const analytics_module_1 = require("./analytics/analytics.module");
const cronHelper_module_1 = require("./utils/cron/cronHelper.module");
const patient_estimate_module_1 = require("./patient-estimate/patient-estimate.module");
const tab_activity_module_1 = require("./tab-activity/tab-activity.module");
const credits_module_1 = require("./credits/credits.module");
const redis_module_1 = require("./utils/redis/redis.module");
const client_dashboard_module_1 = require("./client-dashboard/client-dashboard.module");
const statement_module_1 = require("./statement/statement.module");
const encryption_module_1 = require("./utils/encryption/encryption.module");
const google_calendar_module_1 = require("./google-calendar/google-calendar.module");
let AppModule = AppModule_1 = class AppModule {
    configure(consumer) {
        consumer
            .apply(cors(), logRequest_middleware_1.LoggingMiddleware, rate_limiting_middleware_1.RateLimitingMiddleware, newrelic_middleware_1.NewRelicMiddleware)
            .forRoutes('*');
    }
    static register(config) {
        return {
            module: AppModule_1,
            imports: [sqs_module_1.SqsModule.forRoot(config.isSqsEnabled)]
        };
    }
};
exports.AppModule = AppModule;
exports.AppModule = AppModule = AppModule_1 = __decorate([
    (0, common_1.Module)({
        imports: [
            config_1.ConfigModule.forRoot({
                isGlobal: true,
                load: [
                    app_config_1.default,
                    aws_config_1.default,
                    google_config_1.default,
                    database_config_1.default,
                    config_2.default
                ],
                envFilePath: ['.env']
            }),
            database_module_1.DatabaseModule,
            logger_module_1.LoggerModule,
            middlewares_module_1.MiddlewareModule,
            users_module_1.UsersModule,
            health_module_1.HealthModule,
            aws_module_1.AwsCommonModule,
            auth_module_1.AuthModule,
            version_check_module_1.VersionCheckModule,
            patients_module_1.PatientsModule,
            owners_module_1.OwnersModule,
            clinic_module_1.ClinicModule,
            patientAlerts_module_1.PatientAlertsModule,
            appointments_module_1.AppointmentsModule,
            clinic_lab_report_module_1.ClinicLabReportModule,
            clinic_medications_module_1.ClinicMedicationsModule,
            clinic_plans_module_1.ClinicPlansModule,
            appointment_assessment_module_1.AppointmentAssessmentModule,
            ses_module_1.SESModule,
            // SocketModule,
            user_otps_module_1.UserOtpModule,
            brands_module_1.BrandsModule,
            long_term_medications_module_1.LongTermMedicationsModule,
            s3_module_1.S3Module,
            tasks_module_1.TasksModule,
            ses_module_1.SESModule,
            chat_room_module_1.ChatRoomModule,
            role_module_1.RoleModule,
            clinic_consumables_module_1.ClinicConsumablesModule,
            clinic_products_module_1.ClinicProductsModule,
            clinic_services_module_1.ClinicServicesModule,
            clinic_vaccinations_module_1.ClinicVaccinationsModule,
            clinic_alerts_module_1.ClinicAlertsModule,
            schedule_1.ScheduleModule.forRoot(),
            patient_vaccinations_module_1.PatientVaccinationsModule,
            cart_item_module_1.CartItemModule,
            carts_module_1.CartsModule,
            invoice_module_1.InvoiceModule,
            payment_details_module_1.PaymentDetailsModule,
            data_module_1.DataModule,
            whatsapp_module_1.WhatsappModule,
            clinic_idexx_module_1.ClinicIdexxModule,
            ai_module_1.AIModule,
            patient_reminder_module_1.PatientRemindersModule,
            emr_module_1.EmrModule,
            document_library_module_1.DocumentLibraryModule,
            ai_module_1.AIModule,
            patient_document_libraries_module_1.PatientDocumentLibrariesModule,
            analytics_module_1.AnalyticsModule,
            global_reminders_module_1.GlobalReminderModule,
            diagnostic_note_module_1.DiagnosticTemplatesModule,
            cronHelper_module_1.CronHelperModule.register({ enableCronJobs: false }),
            patient_estimate_module_1.PatientEstimateModule,
            tab_activity_module_1.TabActivityModule,
            credits_module_1.CreditsModule,
            redis_module_1.RedisModule,
            client_dashboard_module_1.ClientDashboardModule,
            statement_module_1.StatementModule,
            encryption_module_1.EncryptionModule,
            google_calendar_module_1.GoogleCalendarModule
        ],
        controllers: [app_controller_1.AppController],
        providers: [app_service_1.AppService]
    })
], AppModule);
//# sourceMappingURL=app.module.js.map