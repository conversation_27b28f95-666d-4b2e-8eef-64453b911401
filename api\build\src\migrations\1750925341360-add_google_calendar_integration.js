"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddGoogleCalendarIntegration1750925341360 = void 0;
class AddGoogleCalendarIntegration1750925341360 {
    constructor() {
        this.name = 'AddGoogleCalendarIntegration1750925341360';
    }
    async up(queryRunner) {
        // Create enum for Google sync status
        await queryRunner.query(`CREATE TYPE "public"."users_google_sync_status_enum" AS ENUM('PENDING', 'SUCCESS', 'FAILED')`);
        // Add Google Calendar integration fields to users table
        await queryRunner.query(`ALTER TABLE "users" ADD "google_calendar_refresh_token" character varying`);
        await queryRunner.query(`ALTER TABLE "users" ADD "google_calendar_id" character varying`);
        await queryRunner.query(`ALTER TABLE "users" ADD "google_sync_token" character varying`);
        await queryRunner.query(`ALTER TABLE "users" ADD "google_webhook_id" character varying`);
        await queryRunner.query(`ALTER TABLE "users" ADD "is_google_sync_enabled" boolean NOT NULL DEFAULT false`);
        await queryRunner.query(`ALTER TABLE "users" ADD "google_sync_status" "public"."users_google_sync_status_enum"`);
        await queryRunner.query(`ALTER TABLE "users" ADD "google_sync_error_message" character varying`);
        await queryRunner.query(`ALTER TABLE "users" ADD "last_google_sync_at" TIMESTAMP WITH TIME ZONE`);
        // Add Google Calendar integration field to appointments table
        await queryRunner.query(`ALTER TABLE "appointments" ADD "google_event_id" character varying`);
    }
    async down(queryRunner) {
        // Remove Google Calendar integration field from appointments table
        await queryRunner.query(`ALTER TABLE "appointments" DROP COLUMN "google_event_id"`);
        // Remove Google Calendar integration fields from users table
        await queryRunner.query(`ALTER TABLE "users" DROP COLUMN "last_google_sync_at"`);
        await queryRunner.query(`ALTER TABLE "users" DROP COLUMN "google_sync_error_message"`);
        await queryRunner.query(`ALTER TABLE "users" DROP COLUMN "google_sync_status"`);
        await queryRunner.query(`ALTER TABLE "users" DROP COLUMN "is_google_sync_enabled"`);
        await queryRunner.query(`ALTER TABLE "users" DROP COLUMN "google_webhook_id"`);
        await queryRunner.query(`ALTER TABLE "users" DROP COLUMN "google_sync_token"`);
        await queryRunner.query(`ALTER TABLE "users" DROP COLUMN "google_calendar_id"`);
        await queryRunner.query(`ALTER TABLE "users" DROP COLUMN "google_calendar_refresh_token"`);
        // Drop enum for Google sync status
        await queryRunner.query(`DROP TYPE "public"."users_google_sync_status_enum"`);
    }
}
exports.AddGoogleCalendarIntegration1750925341360 = AddGoogleCalendarIntegration1750925341360;
//# sourceMappingURL=1750925341360-add_google_calendar_integration.js.map