export declare class GoogleCalendarEventDto {
    id?: string;
    summary?: string;
    description?: string;
    start?: {
        dateTime?: string;
        timeZone?: string;
    };
    end?: {
        dateTime?: string;
        timeZone?: string;
    };
    attendees?: {
        email?: string;
        responseStatus?: string;
    }[];
    reminders?: {
        useDefault?: boolean;
    };
    extendedProperties?: {
        private?: {
            nidanaAppointmentId?: string;
        };
    };
}
