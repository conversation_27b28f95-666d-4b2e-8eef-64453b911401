"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CronHelperService = void 0;
const schedule_1 = require("@nestjs/schedule");
const common_1 = require("@nestjs/common");
const moment = require("moment");
const typeorm_1 = require("typeorm");
const patient_reminder_entity_1 = require("../../patient-reminders/entities/patient-reminder.entity");
const trace_context_1 = require("../middlewares/trace.context");
const sqs_service_1 = require("../aws/sqs/sqs.service");
const typeorm_2 = require("@nestjs/typeorm");
const constants_1 = require("../constants");
const get_login_url_1 = require("../common/get-login-url");
const mail_template_generator_1 = require("../mail-generator/mail-template-generator");
const appointment_entity_1 = require("../../appointments/entities/appointment.entity");
const whatsapp_template_generator_1 = require("../communicatoins/whatsapp-template-generator");
const whatsapp_service_1 = require("../whatsapp-integration/whatsapp.service");
const send_mail_service_1 = require("../aws/ses/send-mail-service");
const winston_logger_service_1 = require("../logger/winston-logger.service");
const redis_service_1 = require("../redis/redis.service");
const enum_appointment_status_1 = require("../../appointments/enums/enum-appointment-status");
const template_helper_util_1 = require("../common/template-helper.util");
const appointment_session_changes_entity_1 = require("../../socket/appointment-session-changes.entity");
const appointment_sessions_entity_1 = require("../../socket/appointment-sessions.entity");
const user_entity_1 = require("../../users/entities/user.entity");
const google_sync_status_enum_1 = require("../../users/enums/google-sync-status.enum");
const google_calendar_service_1 = require("../../google-calendar/google-calendar.service");
let CronHelperService = class CronHelperService {
    constructor(winstonLogger, reminderRepository, sqsService, appointmentRepository, sessionChangeRepository, appointmentSessionsRepository, userRepository, mailService, whatsappService, redisService, googleCalendarService) {
        this.reminderRepository = reminderRepository;
        this.sqsService = sqsService;
        this.appointmentRepository = appointmentRepository;
        this.sessionChangeRepository = sessionChangeRepository;
        this.appointmentSessionsRepository = appointmentSessionsRepository;
        this.userRepository = userRepository;
        this.mailService = mailService;
        this.whatsappService = whatsappService;
        this.redisService = redisService;
        this.googleCalendarService = googleCalendarService;
        // Create a service-specific logger
        this.logger = winstonLogger.createServiceLogger('CronHelperService');
    }
    // Helper method to get Redis client
    getRedisClient() {
        return this.redisService.getClient();
    }
    // Helper method to update reminder tracking data
    async updateReminderTracking(appointmentId, reminderTracking) {
        await this.appointmentRepository.update(appointmentId, {
            reminderTracking
        });
    }
    // Helper method to check if all owner notifications have been sent
    allOwnersNotified(appointment, notificationType) {
        var _a, _b;
        // If no reminder tracking exists yet, we need to send notifications
        if (!((_a = appointment.reminderTracking) === null || _a === void 0 ? void 0 : _a.ownerNotifications)) {
            return false;
        }
        // Check if all owners have received notifications
        const owners = ((_b = appointment === null || appointment === void 0 ? void 0 : appointment.patient) === null || _b === void 0 ? void 0 : _b.patientOwners) || [];
        if (owners.length === 0) {
            return false;
        }
        const statusField = notificationType === 'email' ? 'emailStatus' : 'whatsappStatus';
        // Check each owner has received notification
        return owners.every(owner => {
            var _a, _b, _c;
            const ownerId = (_a = owner === null || owner === void 0 ? void 0 : owner.ownerBrand) === null || _a === void 0 ? void 0 : _a.id;
            if (!ownerId)
                return false;
            const ownerTracking = (_c = (_b = appointment.reminderTracking) === null || _b === void 0 ? void 0 : _b.ownerNotifications) === null || _c === void 0 ? void 0 : _c[ownerId];
            return ownerTracking && ownerTracking[statusField] === 'sent';
        });
    }
    async sendReminderNotifications() {
        const lockKey = 'reminder_cron_lock';
        const timestamp = new Date().toISOString();
        let lockAcquired = false;
        try {
            // Log the start of the process with timestamp
            this.logger.log(`Starting sendReminderNotifications at ${timestamp}`);
            // Check current lock TTL
            const currentTtl = await this.redisService.getTtl(lockKey);
            this.logger.log(`Current lock TTL for patient reminders: ${currentTtl} seconds`, {
                lockKey,
                timestamp,
                ttl: currentTtl
            });
            // If TTL is suspiciously high or there's a stale lock, force clear it
            if (currentTtl > 55 * 60) {
                this.logger.log(`Detected incorrect TTL (${currentTtl}s). Forcing expiration...`, {
                    lockKey,
                    timestamp
                });
                await this.getRedisClient().del(lockKey);
                this.logger.log(`Forced lock deletion complete for ${lockKey}`, {
                    lockKey,
                    timestamp
                });
            }
            // Also clear lock if it's close to expiring but still exists
            if (currentTtl > 0 && currentTtl < 300) {
                // If less than 5 minutes remaining
                this.logger.log(`Lock about to expire (${currentTtl}s remaining). Cleaning up proactively...`, {
                    lockKey,
                    timestamp
                });
                await this.getRedisClient().del(lockKey);
                this.logger.log(`Proactive lock cleanup complete for ${lockKey}`, {
                    lockKey,
                    timestamp
                });
            }
            // Attempt to acquire lock
            const lockExpiry = 30 * 60; // 30 minutes
            const lock = await this.redisService.setLock(lockKey, 'locked', lockExpiry);
            if (!lock) {
                this.logger.log(`Skipping execution. Lock key ${lockKey} still exists.`, {
                    lockKey,
                    timestamp
                });
                return;
            }
            lockAcquired = true;
            this.logger.log(`Lock acquired successfully for ${lockKey}. TTL: ${lockExpiry}s. Proceeding with cron job...`, {
                lockKey,
                timestamp,
                lockTtl: lockExpiry
            });
            const now = moment().add(15, 'hours').startOf('hour').toDate();
            const nextHour = moment().add(15, 'hours').endOf('hour').toDate();
            const reminders = await this.reminderRepository.find({
                where: { dueDate: (0, typeorm_1.Between)(now, nextHour) },
                relations: [
                    'patient',
                    'patient.patientOwners',
                    'patient.patientOwners.ownerBrand',
                    'patient.patientOwners.ownerBrand.globalOwner',
                    'clinic',
                    'clinic.brand'
                ]
            });
            // Filter out reminders for deceased patients
            const activeReminders = reminders.filter(reminder => { var _a; return !((_a = reminder.patient) === null || _a === void 0 ? void 0 : _a.isDeceased); });
            if (reminders.length !== activeReminders.length) {
                const skippedPatients = reminders
                    .filter(reminder => { var _a; return (_a = reminder.patient) === null || _a === void 0 ? void 0 : _a.isDeceased; })
                    .map(reminder => {
                    var _a;
                    return ({
                        patientId: (_a = reminder.patient) === null || _a === void 0 ? void 0 : _a.id,
                        reminderId: reminder.id
                    });
                });
                this.logger.log(`Filtered out ${reminders.length - activeReminders.length} reminders for deceased patients`, {
                    skippedPatients,
                    totalSkipped: skippedPatients.length,
                    timestamp: new Date().toISOString()
                });
            }
            if (activeReminders.length === 0) {
                this.logger.log('No reminders to send for the next day.');
                return;
            }
            this.logger.log(`total no. of reminders:=> ${activeReminders.length}.`);
            // Create minimal reminder info for logging
            const reminderIds = activeReminders.map(reminder => {
                var _a;
                return ({
                    reminderId: reminder.id,
                    patientId: (_a = reminder.patient) === null || _a === void 0 ? void 0 : _a.id
                });
            });
            for (let i = 0; i < activeReminders.length; i += 1) {
                this.sqsService.sendMessage({
                    queueKey: 'NidanaSendDocuments',
                    messageBody: {
                        traceID: trace_context_1.TraceContext.getTraceId(),
                        data: {
                            reminders: [activeReminders[i]],
                            serviceType: 'sendReminderNotification'
                        }
                    },
                    deduplicationId: activeReminders[i].id
                });
                this.logger.log(`Sent sqs message for ${activeReminders[i].id} of reminders.`);
            }
            this.logger.log(`Sent ${activeReminders.length} notifications for reminders due tomorrow.`, {
                timestamp: new Date().toISOString(),
                reminderIds
            });
        }
        catch (error) {
            // Enhanced error logging with more specific information
            this.logger.log('CronHelperService ~ sendReminderNotifications ~ error:', {
                error: error.message || error,
                stack: error.stack,
                errorName: error.name,
                errorCode: error.code,
                timestamp: new Date().toISOString()
            });
        }
        finally {
            // Always release the lock if we acquired it
            if (lockAcquired) {
                try {
                    await this.getRedisClient().del(lockKey);
                    this.logger.log(`Lock ${lockKey} released after execution`, {
                        lockKey,
                        timestamp: new Date().toISOString()
                    });
                }
                catch (redisError) {
                    this.logger.log(`Failed to release lock ${lockKey} after execution:`, {
                        error: redisError.message || redisError,
                        stack: redisError.stack,
                        lockKey,
                        timestamp: new Date().toISOString()
                    });
                }
            }
        }
    }
    async sendAppointmentMailBy24thHour() {
        var _a, _b;
        const lockKey = 'upcoming_appointment_reminder_cron_lock';
        const timestamp = new Date().toISOString();
        let lockAcquired = false;
        // Track minimal notification info for logging
        const notificationsTracking = {
            appointments: [],
            emails: [],
            whatsapp: []
        };
        try {
            // Log the start of the process with timestamp
            this.logger.log(`Starting sendAppointmentMailBy24thHour at ${timestamp}`);
            // Check current lock TTL
            const currentTtl = await this.redisService.getTtl(lockKey);
            this.logger.log(`Current lock TTL for appointment reminders: ${currentTtl} seconds`, {
                lockKey,
                timestamp,
                ttl: currentTtl
            });
            // If TTL is suspiciously high or there's a stale lock, force clear it
            if (currentTtl > 55 * 60) {
                this.logger.log(`Detected incorrect TTL (${currentTtl}s). Forcing expiration...`, {
                    lockKey,
                    timestamp
                });
                await this.getRedisClient().del(lockKey);
                this.logger.log(`Forced lock deletion complete for ${lockKey}`, {
                    lockKey,
                    timestamp
                });
            }
            // Also clear lock if it's close to expiring but still exists
            if (currentTtl > 0 && currentTtl < 300) {
                // If less than 5 minutes remaining
                this.logger.log(`Lock about to expire (${currentTtl}s remaining). Cleaning up proactively...`, {
                    lockKey,
                    timestamp
                });
                await this.getRedisClient().del(lockKey);
                this.logger.log(`Proactive lock cleanup complete for ${lockKey}`, {
                    lockKey,
                    timestamp
                });
            }
            // Attempt to acquire lock
            const lockExpiry = 30 * 60; // 30 minutes
            const lock = await this.redisService.setLock(lockKey, 'locked', lockExpiry);
            if (!lock) {
                this.logger.log(`Skipping execution. Lock key ${lockKey} still exists.`, {
                    lockKey,
                    timestamp
                });
                return;
            }
            lockAcquired = true;
            this.logger.log(`Lock acquired successfully for ${lockKey}. TTL: ${lockExpiry}s. Proceeding with cron job...`, {
                lockKey,
                timestamp,
                lockTtl: lockExpiry
            });
            // Calculate time windows for reminders
            const now = moment();
            // 1. Upcoming appointments (next 16 hours window)
            const upcomingStart = now.clone().add(16, 'hours').startOf('hour');
            const upcomingEnd = now.clone().add(16, 'hours').endOf('hour');
            this.logger.log(
            // Updated log message to reflect only upcoming window
            `Reminder window: Upcoming ${upcomingStart.format('YYYY-MM-DD HH:mm')} to ${upcomingEnd.format('YYYY-MM-DD HH:mm')}`, { timestamp });
            // Query for upcoming appointments using QueryBuilder to combine date and time fields
            // Renamed from upcomingAppointments to appointments as it's the only query now
            const appointments = await this.appointmentRepository
                .createQueryBuilder('appointment')
                .leftJoinAndSelect('appointment.appointmentDoctors', 'appointmentDoctors')
                .leftJoinAndSelect('appointmentDoctors.clinicUser', 'clinicUser')
                .leftJoinAndSelect('clinicUser.user', 'user')
                .leftJoinAndSelect('appointment.patient', 'patient')
                .leftJoinAndSelect('patient.patientOwners', 'patientOwners')
                .leftJoinAndSelect('patientOwners.ownerBrand', 'ownerBrand')
                .leftJoinAndSelect('ownerBrand.globalOwner', 'globalOwner')
                .leftJoinAndSelect('appointment.clinic', 'clinic')
                .leftJoinAndSelect('clinic.brand', 'brand')
                .where('appointment.status = :status', {
                status: enum_appointment_status_1.EnumAppointmentStatus.Scheduled
            })
                // Add condition to filter out deleted appointments
                .andWhere('appointment.deleted_at IS NULL')
                // Combine date and time for proper comparison
                .andWhere(`
					make_timestamp(
						EXTRACT(YEAR FROM appointment.date)::int,
						EXTRACT(MONTH FROM appointment.date)::int,
						EXTRACT(DAY FROM appointment.date)::int,
						EXTRACT(HOUR FROM appointment.start_time)::int,
						EXTRACT(MINUTE FROM appointment.start_time)::int,
						EXTRACT(SECOND FROM appointment.start_time)::double precision
					) BETWEEN :upcomingStart AND :upcomingEnd
				`, {
                // Pass Date objects as parameters
                upcomingStart: now.toDate(),
                upcomingEnd: upcomingEnd.toDate()
            })
                // Added retry logic directly to this query
                .andWhere(`(
					  appointment.reminder_tracking IS NULL OR
					  COALESCE((appointment.reminder_tracking->>'retryCount')::int, 0) < 3
					)`)
                .getMany();
            // Updated log message to reflect only the single query result
            this.logger.log(`Found ${appointments.length} upcoming appointments (including retries) to process`, {
                // Removed missedCount
                upcomingCount: appointments.length,
                timestamp
            });
            // Track appointment IDs for minimal logging
            for (const appointment of appointments) {
                // Skip appointments where all owners have been notified already
                const allEmailsSent = this.allOwnersNotified(appointment, 'email');
                const allWhatsappSent = this.allOwnersNotified(appointment, 'whatsapp');
                if (allEmailsSent && allWhatsappSent) {
                    this.logger.log(`Skipping appointment ${appointment.id} - all notifications already sent`, {
                        appointmentId: appointment.id,
                        timestamp: new Date().toISOString()
                    });
                    continue;
                }
                // Initialize or update reminderTracking
                const currentTracking = appointment.reminderTracking || {};
                const retryCount = currentTracking.retryCount || 0;
                const reminderTracking = {
                    ...currentTracking,
                    lastProcessedAt: new Date().toISOString(),
                    retryCount: retryCount + 1,
                    ownerNotifications: currentTracking.ownerNotifications || {}
                };
                // Update tracking before sending to prevent duplicates on retries
                await this.updateReminderTracking(appointment.id, reminderTracking);
                notificationsTracking.appointments.push({
                    id: appointment.id,
                    patientId: (_a = appointment.patient) === null || _a === void 0 ? void 0 : _a.id
                });
                (_b = appointment === null || appointment === void 0 ? void 0 : appointment.patient) === null || _b === void 0 ? void 0 : _b.patientOwners.forEach(async (patientOwner) => {
                    var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q, _r, _s, _t, _u, _v, _w, _x, _y, _z, _0, _1;
                    // Use ownerBrand branch for appointment reminders
                    const ownerFirstName = ((_a = patientOwner === null || patientOwner === void 0 ? void 0 : patientOwner.ownerBrand) === null || _a === void 0 ? void 0 : _a.firstName) || '';
                    const ownerLastName = ((_b = patientOwner === null || patientOwner === void 0 ? void 0 : patientOwner.ownerBrand) === null || _b === void 0 ? void 0 : _b.lastName) || '';
                    const ownerMobileNumber = `${((_d = (_c = patientOwner === null || patientOwner === void 0 ? void 0 : patientOwner.ownerBrand) === null || _c === void 0 ? void 0 : _c.globalOwner) === null || _d === void 0 ? void 0 : _d.countryCode) || ''}${((_f = (_e = patientOwner === null || patientOwner === void 0 ? void 0 : patientOwner.ownerBrand) === null || _e === void 0 ? void 0 : _e.globalOwner) === null || _f === void 0 ? void 0 : _f.phoneNumber) || ''}`;
                    const ownerId = (_g = patientOwner === null || patientOwner === void 0 ? void 0 : patientOwner.ownerBrand) === null || _g === void 0 ? void 0 : _g.id;
                    // Initialize owner tracking if not exists
                    if (!reminderTracking.ownerNotifications[ownerId]) {
                        reminderTracking.ownerNotifications[ownerId] = {};
                    }
                    // Skip email if already successfully sent to this owner
                    if (((_h = patientOwner === null || patientOwner === void 0 ? void 0 : patientOwner.ownerBrand) === null || _h === void 0 ? void 0 : _h.email) &&
                        !(((_j = reminderTracking.ownerNotifications[ownerId]) === null || _j === void 0 ? void 0 : _j.emailStatus) === 'sent')) {
                        try {
                            const { body, subject, toMailAddress } = (0, mail_template_generator_1.appointmentReminderMailGenerator)({
                                brandName: (_l = (_k = appointment === null || appointment === void 0 ? void 0 : appointment.clinic) === null || _k === void 0 ? void 0 : _k.brand) === null || _l === void 0 ? void 0 : _l.name,
                                contactInformation: ((_p = (_o = (_m = appointment === null || appointment === void 0 ? void 0 : appointment.clinic) === null || _m === void 0 ? void 0 : _m.phoneNumbers) === null || _o === void 0 ? void 0 : _o[0]) === null || _p === void 0 ? void 0 : _p.number) ||
                                    'provided contact no.',
                                petName: (_q = appointment === null || appointment === void 0 ? void 0 : appointment.patient) === null || _q === void 0 ? void 0 : _q.patientName,
                                email: (_r = patientOwner === null || patientOwner === void 0 ? void 0 : patientOwner.ownerBrand) === null || _r === void 0 ? void 0 : _r.email,
                                firstname: ownerFirstName,
                                lastName: ownerLastName,
                                appointmentdate: moment(appointment.date).format('MMMM Do YYYY'),
                                appointmentTime: `${moment(appointment.startTime).add(5, 'hours').add(30, 'minute').format('h:mm a')}`
                            });
                            // Track email recipients separately
                            notificationsTracking.emails.push({
                                appointmentId: appointment.id,
                                patientId: (_s = appointment.patient) === null || _s === void 0 ? void 0 : _s.id,
                                ownerId: (_t = patientOwner.ownerBrand) === null || _t === void 0 ? void 0 : _t.id
                            });
                            if ((0, get_login_url_1.isProduction)()) {
                                this.mailService.sendMail({
                                    body,
                                    subject,
                                    toMailAddress
                                });
                            }
                            else if (!(0, get_login_url_1.isProduction)()) {
                                this.mailService.sendMail({
                                    body,
                                    subject,
                                    toMailAddress: constants_1.DEV_SES_EMAIL
                                });
                            }
                            // Update email status to sent for this owner
                            reminderTracking.ownerNotifications[ownerId] = {
                                ...reminderTracking.ownerNotifications[ownerId],
                                emailStatus: 'sent',
                                emailSentAt: new Date().toISOString()
                            };
                            // Also update top-level status for backwards compatibility
                            reminderTracking.emailStatus = 'sent';
                            reminderTracking.emailSentAt =
                                new Date().toISOString();
                            await this.updateReminderTracking(appointment.id, reminderTracking);
                        }
                        catch (error) {
                            // Log error and update status for this owner
                            this.logger.log(`Failed to send email reminder for appointment ${appointment.id} to owner ${ownerId}`, {
                                error: error.message || String(error),
                                appointmentId: appointment.id,
                                ownerId
                            });
                            reminderTracking.ownerNotifications[ownerId] = {
                                ...reminderTracking.ownerNotifications[ownerId],
                                emailStatus: 'failed',
                                emailError: error.message || String(error)
                            };
                            await this.updateReminderTracking(appointment.id, reminderTracking);
                        }
                    }
                    // Skip WhatsApp if already successfully sent to this owner
                    if (ownerMobileNumber &&
                        !(((_u = reminderTracking.ownerNotifications[ownerId]) === null || _u === void 0 ? void 0 : _u.whatsappStatus) === 'sent') &&
                        (0, get_login_url_1.isProductionOrUat)()) {
                        try {
                            const templateArgs = {
                                appointmentDate: moment(appointment === null || appointment === void 0 ? void 0 : appointment.date).format('MMMM Do YYYY'),
                                appointmentTime: `${moment(appointment === null || appointment === void 0 ? void 0 : appointment.startTime).add(5, 'hours').add(30, 'minute').format('h:mm a')}`,
                                brandName: (_w = (_v = appointment === null || appointment === void 0 ? void 0 : appointment.clinic) === null || _v === void 0 ? void 0 : _v.brand) === null || _w === void 0 ? void 0 : _w.name,
                                contactInformation: ((_z = (_y = (_x = appointment === null || appointment === void 0 ? void 0 : appointment.clinic) === null || _x === void 0 ? void 0 : _x.phoneNumbers) === null || _y === void 0 ? void 0 : _y[0]) === null || _z === void 0 ? void 0 : _z.number) || '',
                                clientName: `${ownerFirstName} ${ownerLastName}`,
                                mobileNumber: ownerMobileNumber,
                                petName: appointment.patient.patientName
                            };
                            // Use the selectTemplate utility function to choose appropriate template
                            const { mobileNumber, templateName, valuesArray } = (0, template_helper_util_1.selectTemplate)(appointment === null || appointment === void 0 ? void 0 : appointment.clinic, templateArgs, whatsapp_template_generator_1.getAppointmentReminderTemplateData, whatsapp_template_generator_1.getAppointmentReminderClinicLinkTemplateData);
                            this.whatsappService.sendTemplateMessage({
                                templateName,
                                valuesArray,
                                mobileNumber
                            });
                            // Track WhatsApp recipients separately
                            notificationsTracking.whatsapp.push({
                                appointmentId: appointment.id,
                                patientId: (_0 = appointment.patient) === null || _0 === void 0 ? void 0 : _0.id,
                                ownerId: (_1 = patientOwner.ownerBrand) === null || _1 === void 0 ? void 0 : _1.id
                            });
                            // Update WhatsApp status to sent for this owner
                            reminderTracking.ownerNotifications[ownerId] = {
                                ...reminderTracking.ownerNotifications[ownerId],
                                whatsappStatus: 'sent',
                                whatsappSentAt: new Date().toISOString()
                            };
                            // Also update top-level status for backwards compatibility
                            reminderTracking.whatsappStatus = 'sent';
                            reminderTracking.whatsappSentAt =
                                new Date().toISOString();
                            await this.updateReminderTracking(appointment.id, reminderTracking);
                        }
                        catch (err) {
                            // Log error and update status for this owner
                            this.logger.log(`Error in sending WhatsApp message for appointment ${appointment.id} to owner ${ownerId}`, {
                                error: err.message || String(err),
                                appointmentId: appointment.id,
                                ownerId
                            });
                            reminderTracking.ownerNotifications[ownerId] = {
                                ...reminderTracking.ownerNotifications[ownerId],
                                whatsappStatus: 'failed',
                                whatsappError: err.message || String(err)
                            };
                            await this.updateReminderTracking(appointment.id, reminderTracking);
                        }
                    }
                });
            }
            // Simplified log with minimal information
            this.logger.log(`Appointment reminders processed: ${notificationsTracking.appointments.length} appointments, ${notificationsTracking.emails.length} emails, ${notificationsTracking.whatsapp.length} WhatsApp messages`, {
                timestamp: new Date().toISOString(),
                appointmentIds: notificationsTracking.appointments,
                emailNotifications: notificationsTracking.emails,
                whatsappNotifications: notificationsTracking.whatsapp
            });
        }
        catch (error) {
            // Enhanced error logging with more specific information
            this.logger.log('CronHelperService ~ sendAppointmentMailBy24thHour ~ error:', {
                error: error.message || error,
                stack: error.stack,
                errorName: error.name,
                errorCode: error.code,
                timestamp: new Date().toISOString()
            });
        }
        finally {
            // Always release the lock if we acquired it
            if (lockAcquired) {
                try {
                    await this.getRedisClient().del(lockKey);
                    this.logger.log(`Lock ${lockKey} released after execution`, {
                        lockKey,
                        timestamp: new Date().toISOString()
                    });
                }
                catch (redisError) {
                    this.logger.log(`Failed to release lock ${lockKey} after execution:`, {
                        error: redisError.message || redisError,
                        stack: redisError.stack,
                        lockKey,
                        timestamp: new Date().toISOString()
                    });
                }
            }
        }
    }
    /**
     * Schedule daily availability validation tasks
     * Runs at 2 AM every day to minimize impact on system performance
     */
    async scheduleAvailabilityDailyValidation() {
        const lockKey = 'availability_daily_validation_lock';
        try {
            // Try to acquire a lock (10 minute expiry)
            const lock = await this.redisService.setLock(lockKey, 'locked', 10 * 60);
            if (!lock) {
                this.logger.log('Skipping daily availability validation - lock exists');
                return;
            }
            this.logger.log('Scheduling daily availability validation tasks');
            await this.sqsService.sendMessage({
                queueKey: 'NidanaAvailabilityMaintenance',
                messageBody: {
                    data: {
                        taskType: 'daily_validation',
                        timestamp: new Date().toISOString()
                    }
                },
                deduplicationId: `availability-daily-validation-${new Date().toISOString().split('T')[0]}`
            });
            this.logger.log('Successfully scheduled daily availability validation');
        }
        catch (error) {
            this.logger.error('Error scheduling daily availability validation', {
                error
            });
            // Release lock in case of error
            await this.getRedisClient().del(lockKey);
        }
    }
    /**
     * Schedule weekly availability defragmentation tasks
     * Runs at 3 AM every Sunday to optimize slots for the upcoming week
     */
    async scheduleAvailabilityWeeklyDefragmentation() {
        const lockKey = 'availability_weekly_defragmentation_lock';
        try {
            // Try to acquire a lock (20 minute expiry)
            const lock = await this.redisService.setLock(lockKey, 'locked', 20 * 60);
            if (!lock) {
                this.logger.log('Skipping weekly availability defragmentation - lock exists');
                return;
            }
            this.logger.log('Scheduling weekly availability defragmentation tasks');
            await this.sqsService.sendMessage({
                queueKey: 'NidanaAvailabilityMaintenance',
                messageBody: {
                    data: {
                        taskType: 'weekly_defragmentation',
                        timestamp: new Date().toISOString()
                    }
                },
                deduplicationId: `availability-weekly-defrag-${new Date().toISOString().split('T')[0]}`
            });
            this.logger.log('Successfully scheduled weekly availability defragmentation');
        }
        catch (error) {
            this.logger.error('Error scheduling weekly availability defragmentation', {
                error
            });
            // Release lock in case of error
            await this.getRedisClient().del(lockKey);
        }
    }
    /**
     * Schedule monthly availability cleanup tasks
     * Runs at 4 AM on the first day of each month
     */
    async scheduleAvailabilityMonthlyCleanup() {
        const lockKey = 'availability_monthly_cleanup_lock';
        try {
            // Try to acquire a lock (30 minute expiry)
            const lock = await this.redisService.setLock(lockKey, 'locked', 30 * 60);
            if (!lock) {
                this.logger.log('Skipping monthly availability cleanup - lock exists');
                return;
            }
            this.logger.log('Scheduling monthly availability cleanup tasks');
            await this.sqsService.sendMessage({
                queueKey: 'NidanaAvailabilityMaintenance',
                messageBody: {
                    data: {
                        taskType: 'monthly_cleanup',
                        timestamp: new Date().toISOString()
                    }
                },
                deduplicationId: `availability-monthly-cleanup-${new Date().toISOString().split('T')[0]}`
            });
            this.logger.log('Successfully scheduled monthly availability cleanup');
        }
        catch (error) {
            this.logger.error('Error scheduling monthly availability cleanup', {
                error
            });
            // Release lock in case of error
            await this.getRedisClient().del(lockKey);
        }
    }
    /**
     * Cleanup stale appointment session changes
     * Runs every 6 hours to remove session changes older than 24 hours
     */
    async cleanupStaleAppointmentSessionChanges() {
        const lockKey = 'appointment_session_changes_cleanup_lock';
        const timestamp = new Date().toISOString();
        let lockAcquired = false;
        try {
            this.logger.log(`Starting cleanupStaleAppointmentSessionChanges at ${timestamp}`);
            // Check current lock TTL
            const currentTtl = await this.redisService.getTtl(lockKey);
            this.logger.log(`Current lock TTL for session changes cleanup: ${currentTtl} seconds`, {
                lockKey,
                timestamp,
                ttl: currentTtl
            });
            // If TTL is suspiciously high or there's a stale lock, force clear it
            if (currentTtl > 25 * 60) {
                this.logger.log(`Detected incorrect TTL (${currentTtl}s). Forcing expiration...`, {
                    lockKey,
                    timestamp
                });
                await this.getRedisClient().del(lockKey);
                this.logger.log(`Forced lock deletion complete for ${lockKey}`, {
                    lockKey,
                    timestamp
                });
            }
            // Attempt to acquire lock
            const lockExpiry = 20 * 60; // 20 minutes
            const lock = await this.redisService.setLock(lockKey, 'locked', lockExpiry);
            if (!lock) {
                this.logger.log(`Skipping execution. Lock key ${lockKey} still exists.`, {
                    lockKey,
                    timestamp
                });
                return;
            }
            lockAcquired = true;
            this.logger.log(`Lock acquired successfully for ${lockKey}. TTL: ${lockExpiry}s. Proceeding with cleanup...`, {
                lockKey,
                timestamp,
                lockTtl: lockExpiry
            });
            // Calculate cutoff time (24 hours ago)
            const cutoffTime = moment().subtract(24, 'hours').toDate();
            this.logger.log(`Cleaning up appointment session changes older than ${cutoffTime.toISOString()}`, {
                cutoffTime: cutoffTime.toISOString(),
                timestamp
            });
            // Delete stale session changes
            const deleteResult = await this.sessionChangeRepository
                .createQueryBuilder()
                .delete()
                .from(appointment_session_changes_entity_1.AppointmentSessionChange)
                .where('updatedAt < :cutoffTime', { cutoffTime })
                .execute();
            this.logger.log(`Successfully cleaned up ${deleteResult.affected || 0} stale appointment session changes`, {
                deletedCount: deleteResult.affected || 0,
                cutoffTime: cutoffTime.toISOString(),
                timestamp: new Date().toISOString()
            });
        }
        catch (error) {
            this.logger.error('CronHelperService ~ cleanupStaleAppointmentSessionChanges ~ error:', {
                error: error.message || error,
                stack: error.stack,
                errorName: error.name,
                errorCode: error.code,
                timestamp: new Date().toISOString()
            });
        }
        finally {
            // Always release the lock if we acquired it
            if (lockAcquired) {
                try {
                    await this.getRedisClient().del(lockKey);
                    this.logger.log(`Lock ${lockKey} released after execution`, {
                        lockKey,
                        timestamp: new Date().toISOString()
                    });
                }
                catch (redisError) {
                    this.logger.log(`Failed to release lock ${lockKey} after execution:`, {
                        error: redisError.message || redisError,
                        stack: redisError.stack,
                        lockKey,
                        timestamp: new Date().toISOString()
                    });
                }
            }
        }
    }
    /**
     * Cleanup stale appointment sessions
     * Runs every 4 hours to remove sessions older than 12 hours
     */
    async cleanupStaleAppointmentSessions() {
        const lockKey = 'appointment_sessions_cleanup_lock';
        const timestamp = new Date().toISOString();
        let lockAcquired = false;
        try {
            this.logger.log(`Starting cleanupStaleAppointmentSessions at ${timestamp}`);
            // Check current lock TTL
            const currentTtl = await this.redisService.getTtl(lockKey);
            this.logger.log(`Current lock TTL for appointment sessions cleanup: ${currentTtl} seconds`, {
                lockKey,
                timestamp,
                ttl: currentTtl
            });
            // If TTL is suspiciously high or there's a stale lock, force clear it
            if (currentTtl > 25 * 60) {
                this.logger.log(`Detected incorrect TTL (${currentTtl}s). Forcing expiration...`, {
                    lockKey,
                    timestamp
                });
                await this.getRedisClient().del(lockKey);
                this.logger.log(`Forced lock deletion complete for ${lockKey}`, {
                    lockKey,
                    timestamp
                });
            }
            // Attempt to acquire lock
            const lockExpiry = 20 * 60; // 20 minutes
            const lock = await this.redisService.setLock(lockKey, 'locked', lockExpiry);
            if (!lock) {
                this.logger.log(`Skipping execution. Lock key ${lockKey} still exists.`, {
                    lockKey,
                    timestamp
                });
                return;
            }
            lockAcquired = true;
            this.logger.log(`Lock acquired successfully for ${lockKey}. TTL: ${lockExpiry}s. Proceeding with cleanup...`, {
                lockKey,
                timestamp,
                lockTtl: lockExpiry
            });
            // Calculate cutoff time (12 hours ago)
            const cutoffTime = moment().subtract(12, 'hours').toDate();
            this.logger.log(`Cleaning up appointment sessions older than ${cutoffTime.toISOString()}`, {
                cutoffTime: cutoffTime.toISOString(),
                timestamp
            });
            // Delete stale appointment sessions
            const deleteResult = await this.appointmentSessionsRepository
                .createQueryBuilder()
                .delete()
                .from(appointment_sessions_entity_1.AppointmentSessions)
                .where('updated_at < :cutoffTime', { cutoffTime })
                .execute();
            this.logger.log(`Successfully cleaned up ${deleteResult.affected || 0} stale appointment sessions`, {
                deletedCount: deleteResult.affected || 0,
                cutoffTime: cutoffTime.toISOString(),
                timestamp: new Date().toISOString()
            });
        }
        catch (error) {
            this.logger.error('CronHelperService ~ cleanupStaleAppointmentSessions ~ error:', {
                error: error.message || error,
                stack: error.stack,
                errorName: error.name,
                errorCode: error.code,
                timestamp: new Date().toISOString()
            });
        }
        finally {
            // Always release the lock if we acquired it
            if (lockAcquired) {
                try {
                    await this.getRedisClient().del(lockKey);
                    this.logger.log(`Lock ${lockKey} released after execution`, {
                        lockKey,
                        timestamp: new Date().toISOString()
                    });
                }
                catch (redisError) {
                    this.logger.log(`Failed to release lock ${lockKey} after execution:`, {
                        error: redisError.message || redisError,
                        stack: redisError.stack,
                        lockKey,
                        timestamp: new Date().toISOString()
                    });
                }
            }
        }
    }
    /**
     * Refresh Google Calendar caches for users whose last sync is stale (>6h).
     * Runs every 2 hours.
     */
    async refreshStaleGoogleCalendarCaches() {
        const threshold = moment().subtract(6, 'hours').toDate();
        try {
            const staleUsers = await this.userRepository.find({
                where: {
                    isGoogleSyncEnabled: true,
                    googleCalendarId: (0, typeorm_1.Raw)(val => `${val} IS NOT NULL`),
                    lastGoogleSyncAt: (0, typeorm_1.Raw)(val => `${val} < :th`, {
                        th: threshold
                    }),
                    googleSyncStatus: google_sync_status_enum_1.GoogleSyncStatus.SUCCESS
                },
                select: ['id']
            });
            if (staleUsers.length === 0)
                return;
            for (const u of staleUsers) {
                await this.sqsService.sendMessage({
                    queueKey: 'NidanaGoogleCalendarSync',
                    messageBody: {
                        data: {
                            type: 'SYNC_REFRESH',
                            userId: u.id,
                            timestamp: new Date().toISOString()
                        }
                    },
                    deduplicationId: `refresh-${u.id}-${Date.now()}`
                });
            }
            this.logger.log(`Queued stale calendar refresh for ${staleUsers.length} users`);
        }
        catch (error) {
            this.logger.error('Failed to queue stale cache refresh', error);
        }
    }
    /**
     * Queue incremental sync for all connected users every 10 minutes.
     */
    async scheduleRegularGoogleSync() {
        try {
            const users = await this.userRepository.find({
                where: {
                    isGoogleSyncEnabled: true,
                    googleCalendarId: (0, typeorm_1.Raw)(val => `${val} IS NOT NULL`),
                    googleSyncStatus: google_sync_status_enum_1.GoogleSyncStatus.SUCCESS
                },
                select: ['id']
            });
            for (const u of users) {
                // Spread messages evenly across the 10-minute window (0-600 s)
                const randomDelay = Math.floor(Math.random() * 600);
                await this.sqsService.sendMessage({
                    queueKey: 'NidanaGoogleCalendarSync',
                    messageBody: {
                        data: {
                            type: 'INCREMENTAL_SYNC',
                            userId: u.id,
                            timestamp: new Date().toISOString()
                        }
                    },
                    deduplicationId: `inc-${u.id}-${Date.now()}`,
                    delaySeconds: randomDelay
                });
            }
            this.logger.log(`Queued incremental Google sync for ${users.length} users`);
        }
        catch (error) {
            this.logger.error('Failed to queue incremental google sync', error);
        }
    }
    /**
     * Renew Google Calendar watch channels that will expire within the next 24 hours.
     * Runs daily at 02:15.
     */
    async renewGoogleWatchChannels() {
        const threshold = moment().add(24, 'hours').toDate();
        try {
            const expiringUsers = await this.userRepository.find({
                where: {
                    isGoogleSyncEnabled: true,
                    googleCalendarId: (0, typeorm_1.Raw)(val => `${val} IS NOT NULL`),
                    googleWebhookId: (0, typeorm_1.Raw)(val => `${val} IS NOT NULL`),
                    googleWebhookExpiresAt: (0, typeorm_1.LessThan)(threshold)
                },
                select: ['id', 'googleCalendarId', 'googleWebhookExpiresAt']
            });
            if (expiringUsers.length === 0)
                return;
            for (const u of expiringUsers) {
                try {
                    await this.googleCalendarService.watchCalendar(u.id, u.googleCalendarId);
                    this.logger.log(`Renewed Google watch channel for user ${u.id} (expires at ${u.googleWebhookExpiresAt})`);
                }
                catch (err) {
                    this.logger.error(`Failed to renew watch channel for user ${u.id}`, err.stack || err);
                }
            }
            this.logger.log(`Attempted to renew watch channels for ${expiringUsers.length} users`);
        }
        catch (error) {
            this.logger.error('Failed to fetch expiring watch channels', error);
        }
    }
};
exports.CronHelperService = CronHelperService;
__decorate([
    (0, schedule_1.Cron)(schedule_1.CronExpression.EVERY_HOUR),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], CronHelperService.prototype, "sendReminderNotifications", null);
__decorate([
    (0, schedule_1.Cron)(schedule_1.CronExpression.EVERY_HOUR),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], CronHelperService.prototype, "sendAppointmentMailBy24thHour", null);
__decorate([
    (0, schedule_1.Cron)('0 2 * * *') // At 2 AM every day
    ,
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], CronHelperService.prototype, "scheduleAvailabilityDailyValidation", null);
__decorate([
    (0, schedule_1.Cron)('0 3 * * 0') // At 3 AM every Sunday
    ,
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], CronHelperService.prototype, "scheduleAvailabilityWeeklyDefragmentation", null);
__decorate([
    (0, schedule_1.Cron)('0 4 1 * *') // At 4 AM on the 1st of every month
    ,
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], CronHelperService.prototype, "scheduleAvailabilityMonthlyCleanup", null);
__decorate([
    (0, schedule_1.Cron)('0 */6 * * *') // Every 6 hours
    ,
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], CronHelperService.prototype, "cleanupStaleAppointmentSessionChanges", null);
__decorate([
    (0, schedule_1.Cron)('0 */4 * * *') // Every 4 hours
    ,
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], CronHelperService.prototype, "cleanupStaleAppointmentSessions", null);
__decorate([
    (0, schedule_1.Cron)('0 */2 * * *') // every 2 hours at minute 0
    ,
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], CronHelperService.prototype, "refreshStaleGoogleCalendarCaches", null);
__decorate([
    (0, schedule_1.Cron)('*/10 * * * *') // every 10 minutes
    ,
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], CronHelperService.prototype, "scheduleRegularGoogleSync", null);
__decorate([
    (0, schedule_1.Cron)('15 2 * * *') // at 02:15 every day
    ,
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], CronHelperService.prototype, "renewGoogleWatchChannels", null);
exports.CronHelperService = CronHelperService = __decorate([
    (0, common_1.Injectable)(),
    __param(1, (0, typeorm_2.InjectRepository)(patient_reminder_entity_1.PatientReminder)),
    __param(2, (0, common_1.Optional)()),
    __param(3, (0, typeorm_2.InjectRepository)(appointment_entity_1.AppointmentEntity)),
    __param(4, (0, typeorm_2.InjectRepository)(appointment_session_changes_entity_1.AppointmentSessionChange)),
    __param(5, (0, typeorm_2.InjectRepository)(appointment_sessions_entity_1.AppointmentSessions)),
    __param(6, (0, typeorm_2.InjectRepository)(user_entity_1.User)),
    __metadata("design:paramtypes", [winston_logger_service_1.WinstonLogger,
        typeorm_1.Repository,
        sqs_service_1.SqsService,
        typeorm_1.Repository,
        typeorm_1.Repository,
        typeorm_1.Repository,
        typeorm_1.Repository,
        send_mail_service_1.SESMailService,
        whatsapp_service_1.WhatsappService,
        redis_service_1.RedisService,
        google_calendar_service_1.GoogleCalendarService])
], CronHelperService);
//# sourceMappingURL=cronHelper.service.js.map